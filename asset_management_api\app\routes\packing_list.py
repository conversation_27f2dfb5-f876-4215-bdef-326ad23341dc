# app/routes/packing_list.py - Master Packing List Blueprint Routes
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text
from app.config.database import get_db
from app.models.user import User
from app.models.masters_consumables import MastersConsumable
from app.middleware.auth import get_current_user, require_admin
import logging
import json
from typing import Optional

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/")
async def get_packing_lists(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all master packing list blueprints.
    Returns data in the format expected by the frontend.
    """
    try:
        # Query the master packing list blueprints directly
        query = text("""
            SELECT id, ref_no, description, status, image, items, 
                   last_updated_by, last_updated_date, created_at, updated_at
            FROM packing_lists 
            ORDER BY created_at DESC
        """)
        
        result = db.execute(query)
        packing_lists = []
        
        for row in result:
            packing_list = {
                "id": str(row.id),
                "refNo": row.ref_no,
                "description": row.description,
                "status": "ON" if row.status else "OFF",
                "image": row.image,
                "lastUpdatedBy": row.last_updated_by,
                "lastUpdated": row.last_updated_date or (row.updated_at.isoformat() if row.updated_at else None),
                "createdAt": row.created_at.isoformat() if row.created_at else None,
                "items": json.loads(row.items) if row.items else {"items": []}
            }
            packing_lists.append(packing_list)
        
        return {
            "success": True,
            "data": {
                "packingLists": packing_lists,
                "count": len(packing_lists)
            }
        }
    except Exception as e:
        logger.error(f"Get packing lists error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/{list_id}")
async def get_packing_list(
    list_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get master packing list blueprint by ID.
    """
    try:
        query = text("""
            SELECT id, ref_no, description, status, image, items, 
                   last_updated_by, last_updated_date, created_at, updated_at
            FROM packing_lists 
            WHERE id = :list_id
        """)
        
        result = db.execute(query, {"list_id": list_id})
        row = result.first()
        
        if not row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        packing_list = {
            "id": str(row.id),
            "refNo": row.ref_no,
            "description": row.description,
            "status": "ON" if row.status else "OFF",
            "image": row.image,
            "lastUpdatedBy": row.last_updated_by,
            "lastUpdated": row.last_updated_date or (row.updated_at.isoformat() if row.updated_at else None),
            "createdAt": row.created_at.isoformat() if row.created_at else None,
            "items": json.loads(row.items) if row.items else {"items": []}
        }
        
        return {
            "success": True,
            "data": {"packingList": packing_list}
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get packing list error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/")
async def create_packing_list(
    packing_list_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Create new master packing list blueprint.
    Auto-generates ref_no in PL#### format.
    """
    try:
        description = packing_list_data.get("description")
        status_value = packing_list_data.get("status", True)
        image = packing_list_data.get("image")
        items = packing_list_data.get("items", [])
        
        if not description:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Description is required"
            )
        
        # Auto-generate ref_no by finding the next available number
        ref_no_query = text("""
            SELECT IFNULL(MAX(CAST(SUBSTRING(ref_no, 3) AS UNSIGNED)), 0) + 1 as next_num
            FROM packing_lists 
            WHERE ref_no REGEXP '^PL[0-9]+$'
        """)
        
        result = db.execute(ref_no_query)
        next_num = result.scalar()
        ref_no = f"PL{next_num:04d}"
        
        # Prepare items JSON
        items_json = json.dumps({"items": items}) if items else json.dumps({"items": []})
        
        # Insert new packing list blueprint
        insert_query = text("""
            INSERT INTO packing_lists (ref_no, description, status, image, items, last_updated_by, last_updated_date)
            VALUES (:ref_no, :description, :status, :image, :items, :updated_by, :updated_date)
        """)
        
        db.execute(insert_query, {
            "ref_no": ref_no,
            "description": description,
            "status": 1 if status_value else 0,
            "image": image,
            "items": items_json,
            "updated_by": f"{current_user.first_name} {current_user.last_name}".strip() or current_user.email,
            "updated_date": "2025-06-08"  # You can modify this to use proper date formatting
        })
        
        db.commit()
        
        # Get the created packing list
        get_query = text("""
            SELECT id, ref_no, description, status, image, items, 
                   last_updated_by, last_updated_date, created_at, updated_at
            FROM packing_lists 
            WHERE ref_no = :ref_no
        """)
        
        result = db.execute(get_query, {"ref_no": ref_no})
        row = result.first()
        
        packing_list = {
            "id": str(row.id),
            "refNo": row.ref_no,
            "description": row.description,
            "status": "ON" if row.status else "OFF",
            "image": row.image,
            "lastUpdatedBy": row.last_updated_by,
            "lastUpdated": row.last_updated_date,
            "createdAt": row.created_at.isoformat() if row.created_at else None,
            "items": json.loads(row.items) if row.items else {"items": []}
        }
        
        return {
            "success": True,
            "message": "Packing list blueprint created successfully",
            "data": {"packingList": packing_list}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Create packing list error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/{list_id}")
async def update_packing_list(
    list_id: str,
    packing_list_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update packing list.
    Equivalent to PUT /:id in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        # Update fields
        for field, value in packing_list_data.items():
            if field == "from":
                packing_list.from_location = value
            elif field == "supplyPackage":
                packing_list.supply_package = value
            elif field == "to":
                packing_list.to = value
            elif hasattr(packing_list, field):
                setattr(packing_list, field, value)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Packing list updated successfully",
            "data": {"packingList": packing_list.to_dict()}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update packing list error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# VERIFICATION STATUS CRUD OPERATIONS

@router.get("/{list_id}/verification")
async def get_verification_items(
    list_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get verification items for a packing list.
    Equivalent to GET /:id/verification in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        verification_items = packing_list.get_verification_items_data()
        
        return {
            "success": True,
            "data": {"verificationItems": verification_items}
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get verification items error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/{list_id}/verification")
async def add_verification_item(
    list_id: str,
    verification_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Add verification item.
    Equivalent to POST /:id/verification in Node.js packingList.ts
    """
    try:
        item = verification_data.get("item")
        status_val = verification_data.get("status")
        verified_by = verification_data.get("verifiedBy", "")
        
        if not item or not status_val:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Item and status are required"
            )
        
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        verification_items = packing_list.get_verification_items_data()
        new_verification_item = {
            "id": str(uuid4()),
            "item": item,
            "status": status_val,
            "verifiedBy": verified_by
        }
        
        verification_items.append(new_verification_item)
        packing_list.set_verification_items_data(verification_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Verification item added successfully",
            "data": {"verificationItem": new_verification_item}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Add verification item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/{list_id}/verification/{verification_id}")
async def update_verification_item(
    list_id: str,
    verification_id: str,
    verification_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update verification item.
    Equivalent to PUT /:id/verification/:verificationId in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        verification_items = packing_list.get_verification_items_data()
        item_index = next((i for i, item in enumerate(verification_items) if item.get("id") == verification_id), -1)
        
        if item_index == -1:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Verification item not found"
            )
        
        verification_items[item_index].update(verification_data)
        packing_list.set_verification_items_data(verification_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Verification item updated successfully",
            "data": {"verificationItem": verification_items[item_index]}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update verification item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.delete("/{list_id}/verification/{verification_id}")
async def delete_verification_item(
    list_id: str,
    verification_id: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete verification item (Undo action).
    Equivalent to DELETE /:id/verification/:verificationId in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        verification_items = packing_list.get_verification_items_data()
        filtered_items = [item for item in verification_items if item.get("id") != verification_id]
        
        if len(filtered_items) == len(verification_items):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Verification item not found"
            )
        
        packing_list.set_verification_items_data(filtered_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Verification item removed successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete verification item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# COMPLETION STATUS CRUD OPERATIONS

@router.get("/{list_id}/completion")
async def get_completion_status(
    list_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get completion status.
    Equivalent to GET /:id/completion in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        completion_status = packing_list.get_completion_status_data()
        if not completion_status:
            completion_status = {
                "allItemsPacked": False,
                "allItemsVerified": False,
                "deliveryInformationAdded": False,
                "finalApproval": False,
                "itemsPackedCount": 0,
                "totalItemsCount": 0,
                "itemsVerifiedCount": 0
            }
        
        return {
            "success": True,
            "data": {"completionStatus": completion_status}
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get completion status error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/{list_id}/completion")
async def update_completion_status(
    list_id: str,
    completion_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update completion status.
    Equivalent to PUT /:id/completion in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        current_completion_status = packing_list.get_completion_status_data()
        updated_completion_status = {**current_completion_status, **completion_data}
        
        packing_list.set_completion_status_data(updated_completion_status)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Completion status updated successfully",
            "data": {"completionStatus": updated_completion_status}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update completion status error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# ITEMS TO UNPACK CRUD OPERATIONS

@router.get("/{list_id}/unpack")
async def get_unpack_items(
    list_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get unpack items for a packing list.
    Equivalent to GET /:id/unpack in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        unpack_items = packing_list.get_unpack_items_data()
        
        return {
            "success": True,
            "data": {"unpackItems": unpack_items}
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get unpack items error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/{list_id}/unpack")
async def add_unpack_item(
    list_id: str,
    unpack_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Add unpack item.
    Equivalent to POST /:id/unpack in Node.js packingList.ts
    """
    try:
        item = unpack_data.get("item")
        status_val = unpack_data.get("status")
        total_qty = unpack_data.get("totalQty")
        unpacked_qty = unpack_data.get("unpackedQty", 0)
        
        if not item or not status_val or total_qty is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Item, status, and totalQty are required"
            )
        
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        unpack_items = packing_list.get_unpack_items_data()
        new_unpack_item = {
            "id": str(uuid4()),
            "item": item,
            "status": status_val,
            "totalQty": total_qty,
            "unpackedQty": unpacked_qty
        }
        
        unpack_items.append(new_unpack_item)
        packing_list.set_unpack_items_data(unpack_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Unpack item added successfully",
            "data": {"unpackItem": new_unpack_item}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Add unpack item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/{list_id}/unpack/{unpack_id}")
async def update_unpack_item(
    list_id: str,
    unpack_id: str,
    unpack_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update unpack item.
    Equivalent to PUT /:id/unpack/:unpackId in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        unpack_items = packing_list.get_unpack_items_data()
        item_index = next((i for i, item in enumerate(unpack_items) if item.get("id") == unpack_id), -1)
        
        if item_index == -1:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Unpack item not found"
            )
        
        # Update the item with new data
        unpack_items[item_index].update(unpack_data)
        
        # Auto-update status based on quantities
        updated_item = unpack_items[item_index]
        unpacked_qty = updated_item.get("unpackedQty", 0)
        total_qty = updated_item.get("totalQty", 0)
        
        if unpacked_qty == 0:
            updated_item["status"] = "Packed"
        elif unpacked_qty >= total_qty:
            updated_item["status"] = "Unpacked"
        else:
            updated_item["status"] = "Partially Unpacked"
        
        packing_list.set_unpack_items_data(unpack_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Unpack item updated successfully",
            "data": {"unpackItem": updated_item}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update unpack item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.delete("/{list_id}/unpack/{unpack_id}")
async def delete_unpack_item(
    list_id: str,
    unpack_id: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete unpack item.
    Equivalent to DELETE /:id/unpack/:unpackId in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        unpack_items = packing_list.get_unpack_items_data()
        filtered_items = [item for item in unpack_items if item.get("id") != unpack_id]
        
        if len(filtered_items) == len(unpack_items):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Unpack item not found"
            )
        
        packing_list.set_unpack_items_data(filtered_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Unpack item removed successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete unpack item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.patch("/{list_id}/unpack/{unpack_id}/quantity")
async def update_unpack_quantity(
    list_id: str,
    unpack_id: str,
    quantity_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Bulk update unpack quantities (for + and - buttons).
    Equivalent to PATCH /:id/unpack/:unpackId/quantity in Node.js packingList.ts
    """
    try:
        action = quantity_data.get("action")  # 'increment' or 'decrement'
        
        if not action or action not in ['increment', 'decrement']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Valid action (increment/decrement) is required"
            )
        
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        unpack_items = packing_list.get_unpack_items_data()
        item_index = next((i for i, item in enumerate(unpack_items) if item.get("id") == unpack_id), -1)
        
        if item_index == -1:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Unpack item not found"
            )
        
        item = unpack_items[item_index]
        unpacked_qty = item.get("unpackedQty", 0)
        total_qty = item.get("totalQty", 0)
        
        if action == 'increment' and unpacked_qty < total_qty:
            item["unpackedQty"] = unpacked_qty + 1
        elif action == 'decrement' and unpacked_qty > 0:
            item["unpackedQty"] = unpacked_qty - 1
        
        # Auto-update status based on quantities
        new_unpacked_qty = item["unpackedQty"]
        if new_unpacked_qty == 0:
            item["status"] = "Packed"
        elif new_unpacked_qty >= total_qty:
            item["status"] = "Unpacked"
        else:
            item["status"] = "Partially Unpacked"
        
        packing_list.set_unpack_items_data(unpack_items)
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Quantity updated successfully",
            "data": {"unpackItem": item}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update quantity error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.delete("/{list_id}")
async def delete_packing_list(
    list_id: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete packing list.
    Equivalent to DELETE /:id in Node.js packingList.ts
    """
    try:
        packing_list = db.query(PackingList).filter(PackingList.id == list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Packing list not found"
            )
        
        db.delete(packing_list)
        db.commit()
        
        return {
            "success": True,
            "message": "Packing list deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete packing list error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# Configuration/Dropdown endpoints for master packing list blueprints

@router.get("/config/items")
async def get_items_dropdown(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get items for dropdown in packing list creation/editing.
    Returns consumables from masters_consumables table.
    """
    try:
        consumables = db.query(MastersConsumable).filter(
            MastersConsumable.status == True
        ).all()
        
        items = []
        for consumable in consumables:
            items.append({
                "id": consumable.id,
                "name": consumable.name,
                "itemNo": consumable.item_no,
                "unit": consumable.unit,
                "category": consumable.category.name if consumable.category else "Unknown"
            })
        
        return {
            "success": True,
            "data": {"items": items}
        }
    except Exception as e:
        logger.error(f"Get items dropdown error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching items"
        )

@router.get("/config/locations")
async def get_locations_dropdown(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get locations for dropdown in packing list creation/editing.
    Returns locations from masters_locations and packing_locations tables.
    """
    try:
        # Get packing locations
        packing_query = text("""
            SELECT id, name, location_type, city, state 
            FROM packing_locations 
            WHERE status = 1
        """)
        packing_result = db.execute(packing_query)
        
        # Get master locations
        master_query = text("""
            SELECT id, name, type, city, state 
            FROM masters_locations 
            WHERE status = 1
        """)
        master_result = db.execute(master_query)
        
        locations = []
        
        # Add packing locations
        for row in packing_result:
            locations.append({
                "id": row.id,
                "name": row.name,
                "type": f"Packing - {row.location_type}" if row.location_type else "Packing Location",
                "city": row.city,
                "state": row.state,
                "source": "packing_locations"
            })
        
        # Add master locations
        for row in master_result:
            locations.append({
                "id": row.id,
                "name": row.name,
                "type": f"Master - {row.type}" if row.type else "Master Location",
                "city": row.city,
                "state": row.state,
                "source": "masters_locations"
            })
        
        # Add some default compartment/storage locations
        default_locations = [
            {"id": "comp1", "name": "Compartment 1", "type": "Storage", "source": "default"},
            {"id": "comp2", "name": "Compartment 2", "type": "Storage", "source": "default"},
            {"id": "comp3", "name": "Compartment 3", "type": "Storage", "source": "default"},
            {"id": "main_storage", "name": "Main Storage", "type": "Storage", "source": "default"},
            {"id": "warehouse_a", "name": "Warehouse A", "type": "Warehouse", "source": "default"},
            {"id": "warehouse_b", "name": "Warehouse B", "type": "Warehouse", "source": "default"}
        ]
        
        locations.extend(default_locations)
        
        return {
            "success": True,
            "data": {"locations": locations}
        }
    except Exception as e:
        logger.error(f"Get locations dropdown error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching locations"
        )