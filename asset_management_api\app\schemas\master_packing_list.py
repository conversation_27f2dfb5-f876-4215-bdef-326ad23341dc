# app/schemas/master_packing_list.py - Master Packing List Blueprint Schemas
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

# Blueprint Item Schema
class PackingListItemBase(BaseModel):
    item: str = Field(..., description="Item name")
    itemNo: str = Field(..., description="Item number/code")
    location: str = Field(..., description="Storage location")
    qty: int = Field(..., ge=0, description="Quantity")

class PackingListItemCreate(PackingListItemBase):
    pass

class PackingListItemUpdate(BaseModel):
    item: Optional[str] = None
    itemNo: Optional[str] = None
    location: Optional[str] = None
    qty: Optional[int] = Field(None, ge=0)

class PackingListItemResponse(PackingListItemBase):
    class Config:
        from_attributes = True

# Master Packing List Blueprint Schemas
class MasterPackingListBase(BaseModel):
    description: str = Field(..., min_length=1, max_length=255, description="Packing list description")
    status: Optional[bool] = Field(True, description="Status - True for ON, False for OFF")
    image: Optional[str] = Field(None, description="Image URL or path")

class MasterPackingListCreate(MasterPackingListBase):
    items: Optional[List[PackingListItemCreate]] = Field([], description="List of items in the blueprint")

class MasterPackingListUpdate(BaseModel):
    description: Optional[str] = Field(None, min_length=1, max_length=255)
    status: Optional[bool] = None
    image: Optional[str] = None
    items: Optional[List[PackingListItemCreate]] = None
    last_updated_by: Optional[str] = None

class MasterPackingListResponse(MasterPackingListBase):
    id: str
    refNo: str
    status: str  # "ON" or "OFF"
    items: Dict[str, List[PackingListItemResponse]]
    lastUpdatedBy: Optional[str] = None
    lastUpdated: Optional[str] = None
    createdAt: Optional[str] = None

    class Config:
        from_attributes = True

# List Response Schema
class MasterPackingListListResponse(BaseModel):
    success: bool
    data: Dict[str, Any]
    total: Optional[int] = None
    message: Optional[str] = None

# Single Response Schema
class MasterPackingListSingleResponse(BaseModel):
    success: bool
    data: Dict[str, MasterPackingListResponse]
    message: Optional[str] = None

# Configuration/Dropdown Schemas
class ItemDropdownResponse(BaseModel):
    id: str
    name: str
    itemNo: str
    unit: str
    category: str

class LocationDropdownResponse(BaseModel):
    id: str
    name: str
    type: str
    city: Optional[str] = None
    state: Optional[str] = None
    source: str

class ConfigurationDropdownResponse(BaseModel):
    success: bool
    data: Dict[str, List[Any]]

# Bulk Operations Schemas
class BulkItemOperation(BaseModel):
    operation: str = Field(..., pattern="^(add|update|remove)$")
    items: List[Dict[str, Any]]

class BulkOperationResponse(BaseModel):
    success: bool
    message: str
    results: Dict[str, Any]

# Export Schema
class ExportRequest(BaseModel):
    format: str = Field(..., pattern="^(pdf|excel|csv)$")
    include_items: bool = Field(True, description="Include items in export")
    filters: Optional[Dict[str, Any]] = None

# Search and Filter Schema
class PackingListFilters(BaseModel):
    search: Optional[str] = None
    status: Optional[bool] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    updated_by: Optional[str] = None
    page: int = Field(1, ge=1)
    limit: int = Field(10, ge=1, le=100)

# Statistics Schema
class PackingListStatistics(BaseModel):
    total_blueprints: int
    active_blueprints: int
    inactive_blueprints: int
    total_items: int
    average_items_per_blueprint: float
    most_used_locations: List[Dict[str, Any]]
    recent_updates: List[Dict[str, Any]]

class StatisticsResponse(BaseModel):
    success: bool
    data: PackingListStatistics

# Validation Error Schema
class ValidationError(BaseModel):
    field: str
    message: str
    code: str

class ErrorResponse(BaseModel):
    success: bool = False
    message: str
    errors: Optional[List[ValidationError]] = None
    code: Optional[str] = None 