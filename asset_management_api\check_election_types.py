from app.config.database import get_db
from sqlalchemy import text

def check_election_types():
    try:
        db = next(get_db())
        
        print("=== ELECTION TYPES TABLE ===")
        result = db.execute(text('SELECT id, name, status FROM election_types'))
        print('ID | Name | Status')
        print('-' * 40)
        for row in result:
            print(f'{row[0]} | {row[1]} | {row[2]}')
        
        print("\n=== ELECTIONS TABLE ===")
        result = db.execute(text('SELECT id, name, election_type_id, status FROM elections'))
        print('ID | Name | Election_Type_ID | Status')
        print('-' * 60)
        for row in result:
            print(f'{row[0]} | {row[1]} | {row[2]} | {row[3]}')
            
        print("\n=== CHECKING RELATIONSHIPS ===")
        result = db.execute(text('''
            SELECT e.id, e.name as election_name, e.election_type_id, et.name as type_name
            FROM elections e
            LEFT JOIN election_types et ON e.election_type_id = et.id
        '''))
        print('Election_ID | Election_Name | Type_ID | Type_Name')
        print('-' * 60)
        for row in result:
            print(f'{row[0]} | {row[1]} | {row[2]} | {row[3] or "NOT FOUND"}')
            
    except Exception as e:
        print(f'Error: {e}')

if __name__ == '__main__':
    check_election_types() 