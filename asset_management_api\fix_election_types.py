import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config.database import get_db
from app.models.election_type import ElectionType
from app.models.elections import Election
from sqlalchemy import text

def fix_election_types():
    try:
        db = next(get_db())
        
        # First, let's see what election types exist
        existing_types = db.query(ElectionType).all()
        print(f"Found {len(existing_types)} existing election types:")
        for et in existing_types:
            print(f"  - {et.id}: {et.name}")
        
        # Create default election types if none exist
        if len(existing_types) == 0:
            print("\nCreating default election types...")
            
            default_types = [
                {"id": "ET001", "name": "General Election"},
                {"id": "ET002", "name": "Primary Election"},
                {"id": "ET003", "name": "Municipal Election"},
                {"id": "ET004", "name": "Special Election"},
                {"id": "ET005", "name": "Runoff Election"}
            ]
            
            for type_data in default_types:
                election_type = ElectionType(
                    id=type_data["id"],
                    name=type_data["name"],
                    status=True
                )
                db.add(election_type)
                print(f"  + Created: {type_data['id']} - {type_data['name']}")
            
            db.commit()
            print("✅ Election types created successfully")
        
        # Now check the elections and their type relationships
        print("\nChecking elections and their types...")
        elections = db.query(Election).all()
        print(f"Found {len(elections)} elections:")
        
        for election in elections:
            print(f"  - {election.id}: {election.name}")
            print(f"    Type ID: {election.election_type_id}")
            
            # Check if the election type exists
            valid_type = db.query(ElectionType).filter(ElectionType.id == election.election_type_id).first()
            if valid_type:
                print(f"    ✅ Valid type: {valid_type.name}")
            else:
                print(f"    ❌ Invalid type ID, updating to ET001")
                election.election_type_id = "ET001"
                db.add(election)
        
        db.commit()
        print("\n✅ All elections now have valid election types")
        
        # Test the join to make sure it works
        print("\nTesting join query...")
        result = db.execute(text('''
            SELECT e.name as election_name, e.election_type_id, et.name as type_name
            FROM elections e
            LEFT JOIN election_types et ON e.election_type_id = et.id
        '''))
        
        for row in result:
            print(f"  - {row[0]} -> {row[2] or 'NOT FOUND'}")
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    fix_election_types() 