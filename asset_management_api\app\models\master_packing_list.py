# app/models/master_packing_list.py - Master Packing List Blueprint Model
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, JSON
from sqlalchemy.sql import func
from app.config.database import Base
import json
from typing import List, Dict, Any, Optional
from datetime import datetime

class MasterPackingList(Base):
    """
    Master Packing List Blueprint Model
    This serves as templates/blueprints for supply checklist packing operations
    """
    __tablename__ = "master_packing_lists"

    id = Column(Integer, primary_key=True, autoincrement=True)
    ref_no = Column(String(255), unique=True, nullable=False, index=True)
    description = Column(String(255), nullable=False)
    status = Column(Boolean, default=True)  # True = ON, False = OFF
    image = Column(String(255), nullable=True)
    items = Column(JSON, nullable=True)  # JSON field to store blueprint items
    last_updated_by = Column(String(255), nullable=True)
    last_updated_date = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def to_dict(self) -> Dict[str, Any]:
        """Convert model to dictionary for API responses."""
        return {
            "id": str(self.id),
            "refNo": self.ref_no,
            "description": self.description,
            "status": "ON" if self.status else "OFF",
            "image": self.image,
            "items": self._get_items_data(),
            "lastUpdatedBy": self.last_updated_by,
            "lastUpdated": self.last_updated_date or (self.updated_at.isoformat() if self.updated_at else None),
            "createdAt": self.created_at.isoformat() if self.created_at else None
        }

    def _get_items_data(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get items data from JSON field."""
        if self.items:
            if isinstance(self.items, str):
                return json.loads(self.items)
            return self.items
        return {"items": []}

    def set_items_data(self, items_data: List[Dict[str, Any]]) -> None:
        """Set items data to JSON field."""
        self.items = {"items": items_data}

    def add_item(self, item_name: str, item_no: str, location: str, qty: int) -> None:
        """Add an item to the blueprint."""
        items_data = self._get_items_data()
        new_item = {
            "item": item_name,
            "itemNo": item_no,
            "location": location,
            "qty": qty
        }
        items_data["items"].append(new_item)
        self.items = items_data

    def remove_item(self, item_no: str) -> bool:
        """Remove an item from the blueprint by item number."""
        items_data = self._get_items_data()
        original_length = len(items_data["items"])
        items_data["items"] = [item for item in items_data["items"] if item.get("itemNo") != item_no]
        
        if len(items_data["items"]) < original_length:
            self.items = items_data
            return True
        return False

    def update_item(self, item_no: str, updates: Dict[str, Any]) -> bool:
        """Update an item in the blueprint."""
        items_data = self._get_items_data()
        for item in items_data["items"]:
            if item.get("itemNo") == item_no:
                item.update(updates)
                self.items = items_data
                return True
        return False

    def get_items_count(self) -> int:
        """Get total number of items in the blueprint."""
        items_data = self._get_items_data()
        return len(items_data.get("items", []))

    def get_total_quantity(self) -> int:
        """Get total quantity of all items in the blueprint."""
        items_data = self._get_items_data()
        return sum(item.get("qty", 0) for item in items_data.get("items", []))

    @classmethod
    def generate_next_ref_no(cls, db_session) -> str:
        """Generate the next available reference number."""
        from sqlalchemy import text
        
        query = text("""
            SELECT IFNULL(MAX(CAST(SUBSTRING(ref_no, 3) AS UNSIGNED)), 0) + 1 as next_num
            FROM master_packing_lists 
            WHERE ref_no REGEXP '^PL[0-9]+$'
        """)
        
        result = db_session.execute(query)
        next_num = result.scalar()
        return f"PL{next_num:04d}"

    def __repr__(self):
        return f"<MasterPackingList(id={self.id}, ref_no='{self.ref_no}', description='{self.description}')>" 