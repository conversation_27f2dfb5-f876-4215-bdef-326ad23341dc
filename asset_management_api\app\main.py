# app/main.py - MINIMAL WORKING VERSION
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import J<PERSON>NResponse
from contextlib import asynccontextmanager
import uvicorn
import os
from dotenv import load_dotenv
from datetime import datetime
import logging

# Import database
from app.config.database import test_connection, close_db_connections, engine

# Import only the working models
from app.models.user import User
from app.models.vendors import Vendor
from app.models.transaction_orders import TransactionOrder
from app.models.rolling_cage import RollingCage
from app.models.masters.location import Location
from app.models.asset_type import AssetType
from app.models.asset_model import AssetModel
from app.models.asset_status import AssetStatus
from app.models.consumables_category import ConsumablesCategory
from app.models.other_cost_type import OtherCostType
from app.models.election_type import ElectionType
from app.models.elections import Election, ElectionFile, ElectionNote
from app.models.packing_location import PackingLocation
from app.models.service_maintenance_type import ServiceMaintenanceType

# Import new workflow models
from app.models.assets import Asset
from app.models.asset_transfers import AssetTransfer
from app.models.damage_reports import DamageReport
from app.models.checkout_sessions import CheckoutSession
from app.models.workflow_state_transitions import WorkflowStateTransition
from app.models.packing_list_items import PackingListItem
from app.models.rolling_cage_items import RollingCageItem
from app.models.packing_list import PackingList

# Import consumables models
from app.models.masters_consumables import MastersConsumable, ConsumableStock, ConsumableTransaction, ConsumableAlert
from app.models.consumables_checkout import ConsumableCheckout, ConsumableCheckoutItem

# Import master packing list model
from app.models.master_packing_list import MasterPackingList

# Import all working routes
from app.routes import auth
from app.routes import transaction_order
from app.routes import vendors as vendor_routes
from app.routes import rolling_cage
from app.routes.masters import location as location_routes
from app.routes import asset_types
from app.routes import asset_model
from app.routes import asset_status
from app.routes import consumables_category
from app.routes import other_cost_types
from app.routes import election_types
from app.routes import elections
from app.routes import packing_location
from app.routes import service_maintenance_types

# Import workflow routes
from app.routes import assets
from app.routes import la_checklist
from app.routes import supply_checklist
from app.routes import asset_status_history

# Import new workflow routes
from app.routes import workflow
from app.routes import asset_transfers
from app.routes import damage_reports
from app.routes import checkout_sessions

# Import consumables routes
from app.routes import masters_consumables
from app.routes import consumables_checkout

# Import new routes for missing frontend functionality
from app.routes import users
from app.routes import reports
from app.routes import maintenance
from app.routes import supply_workflow
from app.routes import packing_page
from app.routes import packing_list
from app.routes import master_packing_list

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan event handler for startup and shutdown."""
    # Startup
    logger.info("🔄 Starting server initialization...")
    
    # Test database connection
    connection_success = test_connection()
    if not connection_success:
        logger.error("❌ Database connection failed. Exiting...")
        raise Exception("Database connection failed")
    
    # Test table accessibility (only for working models)
    try:
        from sqlalchemy import text
        with engine.connect() as conn:
            # Test only the models we have
            conn.execute(text("SELECT COUNT(*) FROM users LIMIT 1"))
            conn.execute(text("SELECT COUNT(*) FROM vendors LIMIT 1"))
            conn.execute(text("SELECT COUNT(*) FROM transaction_orders LIMIT 1"))
            conn.execute(text("SELECT COUNT(*) FROM rolling_cages LIMIT 1"))
            conn.execute(text("SELECT COUNT(*) FROM masters_locations LIMIT 1"))
        
        logger.info("✅ All working database tables are accessible.")
    except Exception as e:
        logger.error("❌ Database tables not found or inaccessible.")
        logger.error(f"Error details: {e}")
        # Don't raise exception, just warn
        logger.warning("⚠️ Some tables may not exist yet. Continuing with startup...")
    
    logger.info("✅ Server initialization completed successfully!")
    
    yield
    
    # Shutdown
    logger.info("🔄 Shutting down server...")
    close_db_connections()
    logger.info("✅ Server shutdown completed.")

# Create FastAPI application
app = FastAPI(
    title="RFI Asset Management API",
    description="Asset Management System API - Converted from Node.js to Python FastAPI",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs",
    redoc_url="/redoc"
    # Removed redirect_slashes=False to allow automatic trailing slash handling
)

# Security middleware
app.add_middleware(GZipMiddleware, minimum_size=1000)

# CORS configuration
allowed_origins = [
    "http://localhost:5173",
    "http://localhost:3000",
]

# Add frontend URL from environment if provided
if frontend_url := os.getenv("FRONTEND_URL"):
    allowed_origins.append(frontend_url)

app.add_middleware(
    CORSMiddleware,
    allow_origins=allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Exception handlers
@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Handle HTTP exceptions."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.detail,
            "status_code": exc.status_code
        }
    )

@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """Handle general exceptions."""
    logger.error(f"Unhandled exception: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "status_code": 500
        }
    )

# Health check endpoint
@app.get("/health", tags=["Health"])
async def health_check():
    """Health check endpoint."""
    return {
        "status": "OK",
        "timestamp": datetime.utcnow().isoformat(),
        "environment": os.getenv("NODE_ENV", "development"),
        "database": "Connected"
    }

# Root endpoint
@app.get("/", tags=["Root"])
async def root():
    """Root endpoint."""
    return {
        "message": "RFI Asset Management API",
        "version": "1.0.0",
        "status": "running",
        "docs": "/docs",
        "redoc": "/redoc"
    }

# API Routes
app.include_router(auth.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(vendor_routes.router, prefix="/api/vendors", tags=["Vendors"])
app.include_router(transaction_order.router, prefix="/api/transaction-orders", tags=["Transaction Orders"])
app.include_router(rolling_cage.router, prefix="/api/rolling-cage", tags=["Rolling Cage"])
app.include_router(location_routes.router, prefix="/api/masters/locations", tags=["Masters Locations"])
app.include_router(asset_types.router, prefix="/api/asset-types", tags=["Asset Types"])
app.include_router(asset_model.router, prefix="/api/asset-models", tags=["Asset Models"])
app.include_router(asset_status.router, prefix="/api/asset-status", tags=["Asset Status"])
app.include_router(consumables_category.router, prefix="/api/consumables-category", tags=["Consumables Category"])
app.include_router(other_cost_types.router, prefix="/api/other-cost-types", tags=["Other Cost Types"])
app.include_router(election_types.router, prefix="/api/election-types", tags=["Election Types"])
app.include_router(elections.router, prefix="/api/elections", tags=["Elections"])
app.include_router(packing_location.router, prefix="/api/packing-locations", tags=["Packing Locations"])
app.include_router(service_maintenance_types.router, prefix="/api/service-maintenance-types", tags=["Service Maintenance Types"])

# Workflow API Routes
app.include_router(assets.router, prefix="/api/assets", tags=["Assets"])
app.include_router(la_checklist.router, prefix="/api/la-checklist", tags=["L&A Checklist"])
app.include_router(supply_checklist.router, prefix="/api/supply-checklist", tags=["Supply Checklist"])
app.include_router(asset_status_history.router, prefix="/api/asset-status-history", tags=["Asset Status History"])

# New Workflow API Routes
app.include_router(workflow.router, prefix="/api/workflow", tags=["Workflow"])
app.include_router(asset_transfers.router, prefix="/api/asset-transfers", tags=["Asset Transfers"])
app.include_router(damage_reports.router, prefix="/api/damage-reports", tags=["Damage Reports"])
app.include_router(checkout_sessions.router, prefix="/api/checkout-sessions", tags=["Checkout Sessions"])

# Consumables API Routes
app.include_router(masters_consumables.router, prefix="/api/masters/consumables", tags=["Masters Consumables"])
app.include_router(consumables_checkout.router, prefix="/api/consumables-checkout", tags=["Consumables Checkout"])

# New API Routes for frontend functionality
app.include_router(users.router, prefix="/api/users", tags=["User Management"])
app.include_router(reports.router, prefix="/api/reports", tags=["Reports & Analytics"])
app.include_router(maintenance.router, prefix="/api/maintenance", tags=["Maintenance"])
app.include_router(supply_workflow.router, prefix="/api/supply-workflow", tags=["Supply Workflow - Pack/Proof/Complete/Unpack"])
app.include_router(packing_page.router, prefix="/api/packing-page", tags=["Packing Page Records"])
app.include_router(packing_list.router, prefix="/api/packing-lists", tags=["Packing Lists"])
app.include_router(master_packing_list.router, prefix="/api/masters/packing-lists", tags=["Master Packing List Blueprints"])

# 404 handler
@app.exception_handler(404)
async def not_found_handler(request: Request, exc: HTTPException):
    """Handle 404 errors."""
    return JSONResponse(
        status_code=404,
        content={
            "success": False,
            "message": "Route not found",
            "path": str(request.url.path)
        }
    )

if __name__ == "__main__":
    # Get configuration from environment
    host = os.getenv("HOST", "0.0.0.0")
    port = int(os.getenv("PORT", 8000))
    debug = os.getenv("NODE_ENV", "development") == "development"
    
    print("🚀 Starting RFI Asset Management API Server...")
    print(f"⏰ Timestamp: {datetime.utcnow().isoformat()}")
    print("")
    print(f"🌐 Server will run on: http://{host}:{port}")
    print(f"📊 Environment: {os.getenv('NODE_ENV', 'development')}")
    print(f"🔗 API Documentation: http://{host}:{port}/docs")
    print(f"📖 ReDoc Documentation: http://{host}:{port}/redoc")
    print(f"🏥 Health Check: http://{host}:{port}/health")
    print("")
    print("📋 Available API Endpoints:")
    print("  GET    /health - Health check")
    print("  POST   /api/auth/login - User authentication")
    print("  GET    /api/transaction-orders - Get transaction orders")
    print("  POST   /api/transaction-orders - Create transaction order")
    print("  GET    /api/vendors - Get vendors")
    print("  POST   /api/vendors - Create vendor")
    print("  GET    /api/rolling-cage - Get rolling cages")
    print("  POST   /api/rolling-cage - Create rolling cage")
    print("  GET    /api/masters/locations - Get master locations")
    print("  POST   /api/masters/locations - Create master location")
    print("  GET    /api/asset-types - Get asset types")
    print("  POST   /api/asset-types - Create asset type")
    print("  GET    /api/asset-models - Get asset models")
    print("  POST   /api/asset-models - Create asset model")
    print("  GET    /api/asset-status - Get asset statuses")
    print("  POST   /api/asset-status - Create asset status")
    print("  GET    /api/consumables-category - Get consumables categories")
    print("  POST   /api/consumables-category - Create consumables category")
    print("  GET    /api/other-cost-types - Get other cost types")
    print("  POST   /api/other-cost-types - Create other cost type")
    print("  GET    /api/election-types - Get election types")
    print("  POST   /api/election-types - Create election type")
    print("  GET    /api/packing-locations - Get packing locations")
    print("  POST   /api/packing-locations - Create packing location")
    print("  GET    /api/service-maintenance-types - Get service maintenance types")
    print("  POST   /api/service-maintenance-types - Create service maintenance type")
    print("")
    print("📋 Asset Workflow API Endpoints:")
    print("  GET    /api/assets - Get assets")
    print("  POST   /api/assets - Create asset")
    print("  GET    /api/la-checklist - Get LA checklist")
    print("  POST   /api/la-checklist - Create LA checklist")
    print("  GET    /api/supply-checklist - Get supply checklist")
    print("  POST   /api/supply-checklist - Create supply checklist")
    print("  GET    /api/asset-status-history - Get asset status history")
    print("  POST   /api/asset-status-history - Create asset status history")
    print("")
    print("🔄 Workflow Management API Endpoints:")
    print("  GET    /api/workflow/transitions - Get valid asset transitions")
    print("  POST   /api/workflow/transition - Execute asset status transition")
    print("  GET    /api/workflow/validate - Validate workflow action")
    print("  GET    /api/asset-transfers - Get asset transfers")
    print("  POST   /api/asset-transfers - Create asset transfer")
    print("  GET    /api/damage-reports - Get damage reports")
    print("  POST   /api/damage-reports - Create damage report")
    print("  GET    /api/checkout-sessions - Get checkout sessions")
    print("  POST   /api/checkout-sessions - Create checkout session")
    print("")
    print("🔗 Database Info:")
    print(f"  Host: {os.getenv('DB_HOST', '**************')}")
    print(f"  Database: {os.getenv('DB_NAME', 'assetmanagementool')}")
    print(f"  User: {os.getenv('DB_USERNAME', 'ram')}")
    print("")
    
    # Run the server
    uvicorn.run(
        "app.main:app",
        host=host,
        port=port,
        reload=debug,
        access_log=True,
        log_level="info" if debug else "warning"
    )