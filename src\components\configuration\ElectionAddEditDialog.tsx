import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { Election, ElectionCreationData } from "@/services/electionService";
import electionTypeService, { ElectionType } from "@/services/electionTypeService";

interface ElectionAddEditDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  election?: Election | null;
  onSave: (data: ElectionCreationData) => Promise<void>;
  isLoading?: boolean;
}

export const ElectionAddEditDialog: React.FC<ElectionAddEditDialogProps> = ({
  open,
  onOpenChange,
  election,
  onSave,
  isLoading = false
}) => {
  const [formData, setFormData] = useState<ElectionCreationData>({
    name: "",
    election_type_id: "",
    election_date: "",
    status: true,
    description: ""
  });

  const [electionTypes, setElectionTypes] = useState<ElectionType[]>([]);
  const [loadingTypes, setLoadingTypes] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [datePickerOpen, setDatePickerOpen] = useState(false);

  // Load election types when dialog opens
  useEffect(() => {
    if (open) {
      loadElectionTypes();
    }
  }, [open]);

  // Populate form when editing
  useEffect(() => {
    if (election) {
      setFormData({
        name: election.name,
        election_type_id: election.election_type_id,
        election_date: election.election_date,
        status: election.status,
        description: election.description || ""
      });
      setSelectedDate(new Date(election.election_date));
    } else {
      // Reset form for adding new election
      setFormData({
        name: "",
        election_type_id: "",
        election_date: "",
        status: true,
        description: ""
      });
      setSelectedDate(undefined);
    }
  }, [election, open]);

  const loadElectionTypes = async () => {
    try {
      setLoadingTypes(true);
      const response = await electionTypeService.getAllElectionTypes();
      // Only show active election types
      setElectionTypes(response.electionTypes.filter(type => type.status));
    } catch (error) {
      console.error('Error loading election types:', error);
      // Fallback to empty array
      setElectionTypes([]);
    } finally {
      setLoadingTypes(false);
    }
  };

  const handleInputChange = (field: keyof ElectionCreationData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      handleInputChange('election_date', format(date, 'yyyy-MM-dd'));
      setDatePickerOpen(false);
    }
  };

  const handleSubmit = async () => {
    if (!formData.name || !formData.election_type_id || !formData.election_date) {
      return; // Basic validation - could add proper error handling
    }

    try {
      await onSave(formData);
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving election:', error);
      // Error handling would be done in parent component
    }
  };

  const handleCancel = () => {
    onOpenChange(false);
  };

  const isEditMode = !!election;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{isEditMode ? 'Edit Election' : 'Election Details'}</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-2 gap-6 py-4">
          {/* Election Name */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Election <span className="text-red-500">*</span>
            </label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Enter election name"
              className="border-gray-300"
            />
          </div>

          {/* Election Type */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Type <span className="text-red-500">*</span>
            </label>
            <Select
              value={formData.election_type_id}
              onValueChange={(value) => handleInputChange("election_type_id", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select an option" />
              </SelectTrigger>
              <SelectContent>
                {loadingTypes ? (
                  <div className="px-2 py-1.5 text-sm text-muted-foreground">Loading...</div>
                ) : electionTypes.length === 0 ? (
                  <div className="px-2 py-1.5 text-sm text-muted-foreground">No election types available</div>
                ) : (
                  electionTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>

          {/* Election Date */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              Date <span className="text-red-500">*</span>
            </label>
            <Popover open={datePickerOpen} onOpenChange={setDatePickerOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !selectedDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? format(selectedDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={handleDateSelect}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>

          {/* Status */}
          <div className="space-y-2">
            <label className="text-sm font-medium">Status <span className="text-red-500">*</span></label>
            <div className="flex items-center space-x-2">
              <Switch
                checked={formData.status}
                onCheckedChange={(checked) => handleInputChange("status", checked)}
              />
              <span className="text-sm">{formData.status ? 'Active' : 'Inactive'}</span>
            </div>
          </div>
        </div>

        {/* Description */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Description</label>
          <Textarea
            value={formData.description}
            onChange={(e) => handleInputChange("description", e.target.value)}
            placeholder="Enter election description (optional)"
            className="border-gray-300"
            rows={3}
          />
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading || !formData.name || !formData.election_type_id || !formData.election_date}>
            {isLoading ? 'Saving...' : 'Save'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}; 