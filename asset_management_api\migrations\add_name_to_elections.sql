-- Migration: Add name column to elections table
-- This migration adds a name column to store election names properly

-- Add the name column
ALTER TABLE elections 
ADD COLUMN name VARCHAR(255) NOT NULL DEFAULT '' AFTER id;

-- Update existing records to have meaningful names based on their date
UPDATE elections 
SET name = CONCAT('Election ', DATE_FORMAT(date, '%M %Y'))
WHERE name = '';

-- Add index on name for better search performance
CREATE INDEX idx_elections_name ON elections(name); 