import React, { useState, useEffect } from "react";
import { AppLayout } from "@/components/layout/AppLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Edit, Eye, Plus, Trash, FileDown, Calendar } from "lucide-react";
import { Link } from "react-router-dom";
import { Election, ElectionCreationData, electionService } from "@/services/electionService";
import { ElectionAddEditDialog } from "@/components/configuration/ElectionAddEditDialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { exportData } from "@/utils/exportUtils";
import { format } from "date-fns";

// Toast notification system
interface ToastData {
  id: string;
  type: 'success' | 'error';
  message: string;
}

// Confirm dialog interface
interface ConfirmDialogData {
  isOpen: boolean;
  title: string;
  message: string;
  onConfirm: () => void;
  onCancel: () => void;
  confirmText?: string;
  cancelText?: string;
  isDestructive?: boolean;
}

// Operation loading states interface
interface OperationStates {
  loading: boolean;
  creating: boolean;
  updating: boolean;
  deleting: Set<string>;
  bulkDeleting: boolean;
}

let toastContainer: React.Dispatch<React.SetStateAction<ToastData[]>> | null = null;

const ToastContainer: React.FC = () => {
  const [toasts, setToasts] = useState<ToastData[]>([]);

  useEffect(() => {
    toastContainer = setToasts;
    return () => {
      toastContainer = null;
    };
  }, []);

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  useEffect(() => {
    toasts.forEach(toast => {
      const timer = setTimeout(() => {
        removeToast(toast.id);
      }, 8000);
      return () => clearTimeout(timer);
    });
  }, [toasts]);

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm">
      {toasts.map(toast => (
        <div
          key={toast.id}
          className={`flex items-center p-4 mb-3 rounded-lg shadow-lg border-l-4 transform transition-all duration-300 ease-in-out ${
            toast.type === 'success' 
              ? 'bg-green-50 border-green-500 text-green-800' 
              : 'bg-red-50 border-red-500 text-red-800'
          }`}
        >
          <span className="flex-1 text-sm font-medium">{toast.message}</span>
          <button
            onClick={() => removeToast(toast.id)}
            className={`ml-3 p-1 rounded-full text-lg font-bold ${
              toast.type === 'success' ? 'hover:bg-green-200' : 'hover:bg-red-200'
            }`}
          >
            ×
          </button>
        </div>
      ))}
    </div>
  );
};

const toast = {
  success: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'success', message }]);
    }
  },
  error: (message: string) => {
    if (toastContainer) {
      const id = Date.now().toString();
      toastContainer(prev => [...prev, { id, type: 'error', message }]);
    }
  }
};

// Confirm Dialog Component
const ConfirmDialog: React.FC<ConfirmDialogData> = ({
  isOpen,
  title,
  message,
  onConfirm,
  onCancel,
  confirmText = "Confirm",
  cancelText = "Cancel",
  isDestructive = false
}) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
        <h3 className="text-lg font-semibold mb-2">{title}</h3>
        <p className="text-gray-600 mb-6">{message}</p>
        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 border border-gray-300 rounded hover:bg-gray-50"
          >
            {cancelText}
          </button>
          <button
            onClick={onConfirm}
            className={`px-4 py-2 text-white rounded ${
              isDestructive 
                ? 'bg-red-600 hover:bg-red-700' 
                : 'bg-blue-600 hover:bg-blue-700'
            }`}
          >
            {confirmText}
          </button>
        </div>
      </div>
    </div>
  );
};

const ElectionsPage = () => {
  const [elections, setElections] = useState<Election[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  
  // Dialog states
  const [addEditDialogOpen, setAddEditDialogOpen] = useState(false);
  const [selectedElection, setSelectedElection] = useState<Election | null>(null);

  // Pagination states
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage] = useState(10);

  // Multi-selection states
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [selectAll, setSelectAll] = useState(false);

  // Operation states
  const [operationStates, setOperationStates] = useState<OperationStates>({
    loading: true,
    creating: false,
    updating: false,
    deleting: new Set(),
    bulkDeleting: false,
  });

  // Confirm dialog state
  const [confirmDialog, setConfirmDialog] = useState<ConfirmDialogData>({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: () => {},
    onCancel: () => {},
  });

  console.log("🔍 ElectionsPage rendering, loading:", operationStates.loading, "elections:", elections.length);

  // Load elections from backend
  const loadElections = async () => {
    try {
      console.log("🚀 Starting API call...");
      setOperationStates(prev => ({ ...prev, loading: true }));
      setError(null);
      
      const response = await electionService.getElections(currentPage, itemsPerPage, searchTerm);
      console.log("📡 API Response:", response);
      
      if (response.success) {
        setElections(response.data.elections || []);
        setTotalPages(response.data.pagination.total_pages);
        setTotalItems(response.data.pagination.total_items);
      } else {
        setError('Failed to load elections');
      }
    } catch (err) {
      console.error('❌ Error loading elections:', err);
      setError(err instanceof Error ? err.message : 'Failed to load elections');
    } finally {
      setOperationStates(prev => ({ ...prev, loading: false }));
      console.log("🏁 Loading finished");
    }
  };

  useEffect(() => {
    console.log("🎯 useEffect triggered");
    loadElections();
  }, [currentPage, searchTerm]);

  // Handle add new election
  const handleAddElection = () => {
    setSelectedElection(null);
    setAddEditDialogOpen(true);
  };

  // Handle edit election
  const handleEditElection = (election: Election) => {
    setSelectedElection(election);
    setAddEditDialogOpen(true);
  };

  // Handle save election (both create and update)
  const handleSaveElection = async (electionData: ElectionCreationData) => {
    try {
      setOperationStates(prev => ({ 
        ...prev, 
        creating: !selectedElection, 
        updating: !!selectedElection 
      }));

      let response;
      if (selectedElection) {
        // Update existing election
        response = await electionService.updateElection(selectedElection.id, electionData);
        if (response.success) {
          toast.success('Election updated successfully!');
        } else {
          toast.error(`Error: ${response.message}`);
        }
      } else {
        // Create new election
        response = await electionService.createElection(electionData);
        if (response.success) {
          toast.success('Election created successfully!');
        } else {
          toast.error(`Error: ${response.message}`);
        }
      }

      if (response.success) {
        loadElections(); // Reload the list
      }

    } catch (err) {
      console.error('Error saving election:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setOperationStates(prev => ({ 
        ...prev, 
        creating: false, 
        updating: false 
      }));
    }
  };

  // Handle delete election
  const handleDeleteElection = async (election: Election) => {
    setConfirmDialog({
      isOpen: true,
      title: 'Delete Election',
      message: `Are you sure you want to delete "${election.name}"? This action cannot be undone.`,
      confirmText: 'Delete',
      cancelText: 'Cancel',
      isDestructive: true,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }));
        await performDelete(election);
      },
      onCancel: () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }));
      },
    });
  };

  // Perform the actual delete operation
  const performDelete = async (election: Election) => {
    try {
      setOperationStates(prev => ({
        ...prev,
        deleting: new Set([...prev.deleting, election.id])
      }));

      const response = await electionService.deleteElection(election.id);

      if (response.success) {
        toast.success('Election deleted successfully!');
        setSelectedItems(prev => {
          const newSet = new Set(prev);
          newSet.delete(election.id);
          return newSet;
        });
        loadElections();
      } else {
        toast.error(`Error: ${response.message}`);
      }
    } catch (err) {
      console.error('Error deleting election:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setOperationStates(prev => {
        const newDeleting = new Set(prev.deleting);
        newDeleting.delete(election.id);
        return { ...prev, deleting: newDeleting };
      });
    }
  };

  // Handle multi-selection
  const handleSelectItem = (id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedItems(new Set());
      setSelectAll(false);
    } else {
      setSelectedItems(new Set(filteredElections.map(item => item.id)));
      setSelectAll(true);
    }
  };

  const filteredElections = elections.filter(election =>
    (election.name?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
    (election.election_type?.name?.toLowerCase() || '').includes(searchTerm.toLowerCase())
  );

  // Update selectAll state when individual items are selected
  useEffect(() => {
    const allSelected = filteredElections.length > 0 && 
      filteredElections.every(item => selectedItems.has(item.id));
    setSelectAll(allSelected);
  }, [selectedItems, filteredElections]);

  // Handle bulk delete
  const handleBulkDelete = () => {
    const selectedElections = elections.filter(election => selectedItems.has(election.id));
    const electionNames = selectedElections.map(election => election.name).join(', ');
    
    setConfirmDialog({
      isOpen: true,
      title: 'Delete Multiple Elections',
      message: `Are you sure you want to delete ${selectedItems.size} election${selectedItems.size === 1 ? '' : 's'}? (${electionNames}) This action cannot be undone.`,
      confirmText: 'Delete All',
      cancelText: 'Cancel',
      isDestructive: true,
      onConfirm: async () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }));
        await performBulkDelete();
      },
      onCancel: () => {
        setConfirmDialog(prev => ({ ...prev, isOpen: false }));
      },
    });
  };

  // Perform bulk delete operation
  const performBulkDelete = async () => {
    try {
      setOperationStates(prev => ({ ...prev, bulkDeleting: true }));

      const selectedElections = elections.filter(election => selectedItems.has(election.id));
      const deletePromises = selectedElections.map(election => 
        electionService.deleteElection(election.id)
      );

      const results = await Promise.allSettled(deletePromises);
      const successful = results.filter(result => 
        result.status === 'fulfilled' && result.value.success
      ).length;

      if (successful === selectedElections.length) {
        toast.success(`Successfully deleted ${successful} election${successful === 1 ? '' : 's'}!`);
      } else if (successful > 0) {
        toast.success(`Deleted ${successful} of ${selectedElections.length} elections. Some deletions failed.`);
      } else {
        toast.error('Failed to delete selected elections');
      }

      setSelectedItems(new Set());
      loadElections();

    } catch (err) {
      console.error('Error in bulk delete:', err);
      toast.error(`Error: ${err instanceof Error ? err.message : 'Unknown error'}`);
    } finally {
      setOperationStates(prev => ({ ...prev, bulkDeleting: false }));
    }
  };

     // Handle export
   const handleExport = (exportFormat: string, data: Election[]) => {
     const exportableData = data.map(election => ({
       'Election': election.name,
       'Type': election.election_type?.name || 'Unknown',
       'Date': format(new Date(election.election_date), 'yyyy-MM-dd'),
       'Status': election.status ? 'Active' : 'Inactive',
       'Description': election.description || '',
       'Created': format(new Date(election.created_at), 'yyyy-MM-dd HH:mm'),
     }));

     const filename = `elections_${format(new Date(), 'yyyy-MM-dd')}`;
     exportData(exportableData, exportFormat, filename, 'Elections');
   };

  // Handle print
  const handlePrint = (data: Election[]) => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      const printContent = `
        <html>
          <head><title>Elections List</title></head>
          <body>
            <h1>Elections</h1>
            <table border="1">
              <tr>
                <th>Election</th>
                <th>Type</th>
                <th>Date</th>
                <th>Status</th>
                <th>Description</th>
              </tr>
              ${data.map(election => `
                <tr>
                  <td>${election.name}</td>
                  <td>${election.election_type?.name || 'Unknown'}</td>
                  <td>${format(new Date(election.election_date), 'yyyy-MM-dd')}</td>
                  <td>${election.status ? 'Active' : 'Inactive'}</td>
                  <td>${election.description || ''}</td>
                </tr>
              `).join('')}
            </table>
          </body>
        </html>
      `;
      printWindow.document.write(printContent);
      printWindow.document.close();
      printWindow.print();
    }
  };

  return (
    <AppLayout>
      <ToastContainer />
      <ConfirmDialog {...confirmDialog} />
      
      <div className="flex flex-col h-full">
        <div className="flex items-center justify-between bg-gray-100 p-4 border-b">
          <h1 className="text-xl font-semibold">Elections</h1>
          <div className="flex items-center text-sm text-gray-500">
            <Link to="/masters" className="hover:underline">Masters</Link>
            <span className="mx-2">›</span>
            <span>Elections</span>
          </div>
        </div>

        <div className="p-4">
          <div className="flex justify-between items-center mb-6">
            <div className="relative w-64">
              <Input
                type="text"
                placeholder="Search..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <svg
                className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            
            <div className="flex items-center space-x-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <FileDown className="h-4 w-4 mr-2" />
                    Export
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => handleExport('excel', filteredElections)}>
                    Export to Excel
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleExport('csv', filteredElections)}>
                    Export to CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handlePrint(filteredElections)}>
                    Print List
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <Button 
                variant="default" 
                size="sm" 
                onClick={handleAddElection}
                disabled={operationStates.loading}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Election
              </Button>
            </div>
          </div>

          {/* Bulk actions */}
          {selectedItems.size > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-blue-800">
                  {selectedItems.size} election{selectedItems.size === 1 ? '' : 's'} selected
                </span>
                <div className="flex space-x-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleBulkDelete}
                    disabled={operationStates.bulkDeleting}
                  >
                    <Trash className="h-4 w-4 mr-1" />
                    {operationStates.bulkDeleting ? 'Deleting...' : 'Delete Selected'}
                  </Button>
                </div>
              </div>
            </div>
          )}

          {/* Loading State */}
          {operationStates.loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p className="text-gray-600">Loading elections...</p>
              </div>
            </div>
          ) : error ? (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center">
                <svg className="h-5 w-5 text-red-600 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <p className="text-red-800">{error}</p>
              </div>
            </div>
          ) : (
            <div className="border rounded-lg shadow-sm overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-left py-3 px-4 font-medium w-12">
                      <input
                        type="checkbox"
                        checked={selectAll}
                        onChange={handleSelectAll}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </th>
                    <th className="text-left py-3 px-4 font-medium">Election</th>
                    <th className="text-left py-3 px-4 font-medium">Type</th>
                    <th className="text-left py-3 px-4 font-medium">Date</th>
                    <th className="text-left py-3 px-4 font-medium">Status</th>
                    <th className="text-right py-3 px-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredElections.length === 0 ? (
                    <tr>
                      <td colSpan={6} className="py-8 px-4 text-center text-gray-500">
                        No elections found. Click "Add Election" to create one.
                      </td>
                    </tr>
                  ) : (
                    filteredElections.map((election) => (
                      <tr key={election.id} className="border-t hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <input
                            type="checkbox"
                            checked={selectedItems.has(election.id)}
                            onChange={() => handleSelectItem(election.id)}
                            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          />
                        </td>
                        <td className="py-3 px-4 font-medium">{election.name}</td>
                        <td className="py-3 px-4">{election.election_type?.name || 'Unknown'}</td>
                        <td className="py-3 px-4">
                          {format(new Date(election.election_date), 'MMM dd, yyyy')}
                        </td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            election.status ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                          }`}>
                            {election.status ? "Active" : "Inactive"}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex justify-end space-x-2">
                            <button
                              onClick={() => handleEditElection(election)}
                              className="p-1 hover:bg-gray-100 rounded"
                              disabled={operationStates.deleting.has(election.id)}
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleDeleteElection(election)}
                              className="p-1 hover:bg-gray-100 rounded text-red-600"
                              disabled={operationStates.deleting.has(election.id)}
                            >
                              {operationStates.deleting.has(election.id) ? (
                                <div className="animate-spin h-4 w-4 border-2 border-red-600 border-t-transparent rounded-full"></div>
                              ) : (
                                <Trash className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          )}

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-gray-600">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} entries
              </div>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1 || operationStates.loading}
                >
                  Previous
                </Button>
                <span className="flex items-center px-3 py-1 text-sm">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages || operationStates.loading}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add/Edit Election Dialog */}
      <ElectionAddEditDialog
        open={addEditDialogOpen}
        onOpenChange={setAddEditDialogOpen}
        election={selectedElection}
        onSave={handleSaveElection}
        isLoading={operationStates.creating || operationStates.updating}
      />
    </AppLayout>
  );
};

export default ElectionsPage;