import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config.database import get_db
from app.models.election_type import ElectionType
from uuid import uuid4

def create_election_types():
    try:
        db = next(get_db())
        
        # Check if election types already exist
        existing_types = db.query(ElectionType).all()
        print(f"Found {len(existing_types)} existing election types")
        for et in existing_types:
            print(f"  - {et.id}: {et.name}")
        
        # Create default election types if none exist
        if len(existing_types) == 0:
            print("\nCreating default election types...")
            
            default_types = [
                {"id": "ET001", "name": "General Election"},
                {"id": "ET002", "name": "Primary Election"},
                {"id": "ET003", "name": "Municipal Election"},
                {"id": "ET004", "name": "Special Election"},
                {"id": "ET005", "name": "Runoff Election"}
            ]
            
            for type_data in default_types:
                election_type = ElectionType(
                    id=type_data["id"],
                    name=type_data["name"],
                    status=True
                )
                db.add(election_type)
                print(f"  + Created: {type_data['id']} - {type_data['name']}")
            
            db.commit()
            print("✅ Election types created successfully")
        else:
            print("✅ Election types already exist")
            
        # Now update the existing elections to use a valid election type
        from app.models.elections import Election
        elections = db.query(Election).all()
        print(f"\nFound {len(elections)} elections:")
        
        for election in elections:
            print(f"  - {election.id}: {election.name} (Type: {election.election_type_id})")
            
            # If election_type_id is not valid, update it to ET001
            valid_type = db.query(ElectionType).filter(ElectionType.id == election.election_type_id).first()
            if not valid_type:
                print(f"    ⚠️ Invalid type ID {election.election_type_id}, updating to ET001")
                election.election_type_id = "ET001"
                db.add(election)
        
        db.commit()
        print("✅ Elections updated successfully")
        
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    create_election_types() 