import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.config.database import get_db
from sqlalchemy import text

def debug_election_types():
    try:
        db = next(get_db())
        
        print("=== ELECTION TYPES ===")
        result = db.execute(text('SELECT id, name FROM election_types ORDER BY id'))
        for row in result:
            print(f'ID: {row[0]} | Name: {row[1]}')
        
        print("\n=== ELECTIONS ===")
        result = db.execute(text('SELECT id, name, election_type_id FROM elections ORDER BY id'))
        for row in result:
            print(f'ID: {row[0]} | Name: {row[1]} | Type_ID: {row[2]}')
            
        print("\n=== JOIN TEST ===")
        result = db.execute(text('''
            SELECT e.name as election_name, e.election_type_id, et.id as type_id, et.name as type_name
            FROM elections e
            LEFT JOIN election_types et ON e.election_type_id = et.id
        '''))
        for row in result:
            print(f'Election: {row[0]} | Type_ID: {row[1]} | Type_ID_Found: {row[2]} | Type_Name: {row[3] or "NOT FOUND"}')
    
    except Exception as e:
        print(f'Error: {e}')
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_election_types() 