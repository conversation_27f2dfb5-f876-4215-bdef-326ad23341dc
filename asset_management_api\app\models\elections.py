# app/models/elections.py
from sqlalchemy import Column, String, Boolean, DateTime, Date, Text, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.config.database import Base

class Election(Base):
    __tablename__ = "elections"

    id = Column(String(255), primary_key=True)
    name = Column(String(255), nullable=False)
    election_type_id = Column(String(255), ForeignKey("election_types.id"), nullable=False)
    date = Column(DateTime, nullable=False)
    status = Column(String(50), nullable=False, default='Active')  # enum: Active, Inactive, Completed, Cancelled
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, nullable=False, server_default=func.now())
    updated_at = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now())

    # Relationships
    election_type = relationship("ElectionType", back_populates="elections")
    election_files = relationship("ElectionFile", back_populates="election", cascade="all, delete-orphan")
    election_notes = relationship("ElectionNote", back_populates="election", cascade="all, delete-orphan")

    def to_dict(self) -> dict:
        """Convert election object to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "election_type_id": self.election_type_id,
            "election_type": self.election_type.to_dict() if self.election_type else None,
            "election_date": self.date.isoformat() if self.date else None,
            "status": self.status == 'Active',  # Convert string status to boolean for frontend compatibility
            "description": self.description,
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

class ElectionFile(Base):
    __tablename__ = "election_files"

    id = Column(String(36), primary_key=True)
    election_id = Column(String(36), ForeignKey("elections.id"), nullable=False)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(String(50), nullable=True)
    uploaded_by = Column(String(36), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    election = relationship("Election", back_populates="election_files")
    user = relationship("User")

    def to_dict(self) -> dict:
        """Convert election file object to dictionary."""
        return {
            "id": self.id,
            "election_id": self.election_id,
            "file_name": self.file_name,
            "file_path": self.file_path,
            "file_size": self.file_size,
            "uploaded_by": self.uploaded_by,
            "uploaded_by_name": self.user.username if self.user else None,
            "created_at": self.created_at
        }

class ElectionNote(Base):
    __tablename__ = "election_notes"

    id = Column(String(36), primary_key=True)
    election_id = Column(String(36), ForeignKey("elections.id"), nullable=False)
    note_text = Column(Text, nullable=False)
    created_by = Column(String(36), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # Relationships
    election = relationship("Election", back_populates="election_notes")
    user = relationship("User")

    def to_dict(self) -> dict:
        """Convert election note object to dictionary."""
        return {
            "id": self.id,
            "election_id": self.election_id,
            "note_text": self.note_text,
            "created_by": self.created_by,
            "created_by_name": self.user.username if self.user else None,
            "created_by_avatar": "",  # Add avatar field if needed
            "created_at": self.created_at
        }
