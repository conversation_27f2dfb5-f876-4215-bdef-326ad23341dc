#!/usr/bin/env python3
"""Simple script to run the packing lists table migration"""

import mysql.connector
import os
from urllib.parse import urlparse
from app.config.database import get_db
from sqlalchemy import text

def run_migration():
    """Run the SQL migration to add the election column."""
    
    # Parse database URL from environment or use default
    database_url = os.getenv('DATABASE_URL', 'mysql+mysqlconnector://root:root@localhost:3306/asset_management')
    
    # Parse the URL to get connection parameters
    parsed = urlparse(database_url.replace('mysql+mysqlconnector://', 'mysql://'))
    
    connection_config = {
        'host': parsed.hostname or 'localhost',
        'port': parsed.port or 3306,
        'user': parsed.username or 'root',
        'password': parsed.password or 'root',
        'database': parsed.path.lstrip('/') or 'asset_management'
    }
    
    try:
        # Connect to database
        print("Connecting to database...")
        conn = mysql.connector.connect(**connection_config)
        cursor = conn.cursor()
        
        # Check if column exists
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'packing_lists' 
            AND COLUMN_NAME = 'election'
        """)
        
        column_exists = cursor.fetchone()[0] > 0
        
        if not column_exists:
            print("Adding missing 'election' column...")
            cursor.execute("""
                ALTER TABLE packing_lists 
                ADD COLUMN election VARCHAR(255) NOT NULL DEFAULT 'General Election 2024'
            """)
            conn.commit()
            print("✅ Successfully added 'election' column to packing_lists table")
        else:
            print("✅ 'election' column already exists in packing_lists table")
        
        # Verify the table structure
        cursor.execute("DESCRIBE packing_lists")
        columns = cursor.fetchall()
        print(f"\nCurrent packing_lists table columns: {len(columns)}")
        for col in columns:
            print(f"  - {col[0]} ({col[1]})")
            
    except mysql.connector.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
    
    return True

def run_migration_sqlalchemy():
    try:
        db = next(get_db())
        
        print("Running migration: Add name column to elections table...")
        
        # Add the name column
        print("1. Adding name column...")
        db.execute(text("""
            ALTER TABLE elections 
            ADD COLUMN name VARCHAR(255) NOT NULL DEFAULT '' AFTER id
        """))
        
        # Update existing records to have meaningful names based on their date
        print("2. Updating existing records with names...")
        db.execute(text("""
            UPDATE elections 
            SET name = CONCAT('Election ', DATE_FORMAT(date, '%M %Y'))
            WHERE name = ''
        """))
        
        # Add index on name for better search performance
        print("3. Adding index on name column...")
        db.execute(text("""
            CREATE INDEX idx_elections_name ON elections(name)
        """))
        
        # Commit the changes
        db.commit()
        print("✅ Migration completed successfully!")
        
        # Verify the changes
        print("\nVerifying migration...")
        result = db.execute(text('DESCRIBE elections'))
        print('Updated elections table schema:')
        print('Column | Type | Null | Key | Default | Extra')
        print('-' * 60)
        for row in result:
            print(f'{row[0]} | {row[1]} | {row[2]} | {row[3]} | {row[4]} | {row[5]}')
            
    except Exception as e:
        print(f'❌ Migration failed: {e}')
        db.rollback()

if __name__ == "__main__":
    print("Running packing lists table migration...")
    success = run_migration()
    if success:
        print("\n🎉 Migration completed successfully!")
    else:
        print("\n💥 Migration failed!")

    print("\nRunning SQLAlchemy migration...")
    run_migration_sqlalchemy() 