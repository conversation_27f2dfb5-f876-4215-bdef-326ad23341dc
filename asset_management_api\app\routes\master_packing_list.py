# app/routes/master_packing_list.py - Master Packing List Blueprint Routes
from fastapi import APIRouter, Depends, HTTPException, status, Query, File, UploadFile
from sqlalchemy.orm import Session
from sqlalchemy import text, and_, or_
from app.config.database import get_db
from app.models.master_packing_list import MasterPackingList
from app.models.user import User
from app.models.masters_consumables import MastersConsumable
from app.middleware.auth import get_current_user, require_admin
from app.schemas.master_packing_list import (
    MasterPackingListCreate, MasterPackingListUpdate, MasterPackingListResponse,
    MasterPackingListListResponse, MasterPackingListSingleResponse,
    PackingListItemCreate, PackingListItemUpdate, PackingListFilters,
    ConfigurationDropdownResponse, ErrorResponse
)
import logging
from typing import Optional, List
from datetime import datetime

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/")
async def get_master_packing_lists(
    search: Optional[str] = Query(None, description="Search in description or ref_no"),
    status_filter: Optional[bool] = Query(None, description="Filter by status (true=ON, false=OFF)", alias="status"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all master packing list blueprints with optional filtering and pagination.
    """
    try:
        # Build query
        query = db.query(MasterPackingList)
        
        # Apply filters
        if search:
            search_filter = f"%{search}%"
            query = query.filter(
                or_(
                    MasterPackingList.description.like(search_filter),
                    MasterPackingList.ref_no.like(search_filter)
                )
            )
        
        if status_filter is not None:
            query = query.filter(MasterPackingList.status == status_filter)
        
        # Get total count before pagination
        total_count = query.count()
        
        # Apply pagination
        offset = (page - 1) * limit
        packing_lists = query.order_by(MasterPackingList.created_at.desc()).offset(offset).limit(limit).all()
        
        # Convert to response format
        packing_list_data = [packing_list.to_dict() for packing_list in packing_lists]
        
        return {
            "success": True,
            "data": {
                "packingLists": packing_list_data,
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total_count,
                    "pages": (total_count + limit - 1) // limit
                }
            },
            "total": total_count
        }
        
    except Exception as e:
        logger.error(f"Get master packing lists error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching master packing lists"
        )

@router.get("/{packing_list_id}")
async def get_master_packing_list(
    packing_list_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific master packing list blueprint by ID.
    """
    try:
        packing_list = db.query(MasterPackingList).filter(MasterPackingList.id == packing_list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Master packing list not found"
            )
        
        return {
            "success": True,
            "data": {"packingList": packing_list.to_dict()}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get master packing list error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching master packing list"
        )

@router.post("/")
async def create_master_packing_list(
    packing_list_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Create a new master packing list blueprint.
    Auto-generates ref_no in PL#### format.
    """
    try:
        # Generate next ref_no
        ref_no = MasterPackingList.generate_next_ref_no(db)
        
        # Process items and auto-assign item numbers
        items = packing_list_data.get("items", [])
        processed_items = []
        for i, item in enumerate(items, 1):
            processed_item = {
                "item": item.get("item"),
                "itemNo": f"{i:03d}",  # Auto-generate item numbers: 001, 002, 003...
                "location": item.get("location"),
                "qty": int(item.get("qty", 0))
            }
            processed_items.append(processed_item)
        
        # Create new master packing list
        packing_list = MasterPackingList(
            ref_no=ref_no,
            description=packing_list_data.get("description"),
            status=packing_list_data.get("status", True),
            image=packing_list_data.get("image"),
            last_updated_by=f"{current_user.first_name} {current_user.last_name}".strip() or current_user.email,
            last_updated_date=datetime.now().strftime("%Y-%m-%d")
        )
        
        # Set processed items
        if processed_items:
            packing_list.set_items_data(processed_items)
        
        db.add(packing_list)
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "data": {"packingList": packing_list.to_dict()},
            "message": "Master packing list blueprint created successfully"
        }
        
    except Exception as e:
        logger.error(f"Create master packing list error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating master packing list"
        )

@router.put("/{packing_list_id}")
async def update_master_packing_list(
    packing_list_id: int,
    packing_list_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update a master packing list blueprint.
    """
    try:
        packing_list = db.query(MasterPackingList).filter(MasterPackingList.id == packing_list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Master packing list not found"
            )
        
        # Update fields
        if "description" in packing_list_data:
            packing_list.description = packing_list_data["description"]
        if "status" in packing_list_data:
            packing_list.status = packing_list_data["status"]
        if "image" in packing_list_data:
            packing_list.image = packing_list_data["image"]
        if "items" in packing_list_data:
            packing_list.set_items_data(packing_list_data["items"])
        
        # Update metadata
        packing_list.last_updated_by = f"{current_user.first_name} {current_user.last_name}".strip() or current_user.email
        packing_list.last_updated_date = datetime.now().strftime("%Y-%m-%d")
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "data": {"packingList": packing_list.to_dict()},
            "message": "Master packing list blueprint updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update master packing list error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating master packing list"
        )

@router.delete("/{packing_list_id}")
async def delete_master_packing_list(
    packing_list_id: int,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete a master packing list blueprint.
    """
    try:
        packing_list = db.query(MasterPackingList).filter(MasterPackingList.id == packing_list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Master packing list not found"
            )
        
        db.delete(packing_list)
        db.commit()
        
        return {
            "success": True,
            "message": "Master packing list blueprint deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete master packing list error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting master packing list"
        )

# Item Management Endpoints
@router.post("/{packing_list_id}/items")
async def add_item_to_packing_list(
    packing_list_id: int,
    item_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Add an item to a master packing list blueprint.
    """
    try:
        packing_list = db.query(MasterPackingList).filter(MasterPackingList.id == packing_list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Master packing list not found"
            )
        
        # Get current items
        current_items = packing_list._get_items_data().get("items", [])
        
        # Generate next item number
        next_item_no = f"{len(current_items) + 1:03d}"
        
        # Add new item
        new_item = {
            "item": item_data.get("item"),
            "itemNo": next_item_no,
            "location": item_data.get("location"),
            "qty": int(item_data.get("qty", 0))
        }
        
        current_items.append(new_item)
        packing_list.set_items_data(current_items)
        
        # Update metadata
        packing_list.last_updated_by = f"{current_user.first_name} {current_user.last_name}".strip() or current_user.email
        packing_list.last_updated_date = datetime.now().strftime("%Y-%m-%d")
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Item added successfully",
            "data": {"packingList": packing_list.to_dict()}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Add item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error adding item to packing list"
        )

@router.put("/{packing_list_id}/items/{item_no}")
async def update_item_in_packing_list(
    packing_list_id: int,
    item_no: str,
    item_data: dict,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Update an item in a master packing list blueprint.
    """
    try:
        packing_list = db.query(MasterPackingList).filter(MasterPackingList.id == packing_list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Master packing list not found"
            )
        
        # Get current items
        current_items = packing_list._get_items_data().get("items", [])
        
        # Find and update the item
        item_found = False
        for item in current_items:
            if item.get("itemNo") == item_no:
                if "item" in item_data:
                    item["item"] = item_data["item"]
                if "location" in item_data:
                    item["location"] = item_data["location"]
                if "qty" in item_data:
                    item["qty"] = int(item_data["qty"])
                item_found = True
                break
        
        if not item_found:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Item not found in packing list"
            )
        
        packing_list.set_items_data(current_items)
        
        # Update metadata
        packing_list.last_updated_by = f"{current_user.first_name} {current_user.last_name}".strip() or current_user.email
        packing_list.last_updated_date = datetime.now().strftime("%Y-%m-%d")
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Item updated successfully",
            "data": {"packingList": packing_list.to_dict()}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating item in packing list"
        )

@router.delete("/{packing_list_id}/items/{item_no}")
async def remove_item_from_packing_list(
    packing_list_id: int,
    item_no: str,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Remove an item from a master packing list blueprint.
    """
    try:
        packing_list = db.query(MasterPackingList).filter(MasterPackingList.id == packing_list_id).first()
        
        if not packing_list:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Master packing list not found"
            )
        
        # Get current items
        current_items = packing_list._get_items_data().get("items", [])
        
        # Remove the item
        original_length = len(current_items)
        current_items = [item for item in current_items if item.get("itemNo") != item_no]
        
        if len(current_items) == original_length:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Item not found in packing list"
            )
        
        # Re-number items to maintain sequential numbering
        for i, item in enumerate(current_items, 1):
            item["itemNo"] = f"{i:03d}"
        
        packing_list.set_items_data(current_items)
        
        # Update metadata
        packing_list.last_updated_by = f"{current_user.first_name} {current_user.last_name}".strip() or current_user.email
        packing_list.last_updated_date = datetime.now().strftime("%Y-%m-%d")
        
        db.commit()
        db.refresh(packing_list)
        
        return {
            "success": True,
            "message": "Item removed successfully",
            "data": {"packingList": packing_list.to_dict()}
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Remove item error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error removing item from packing list"
        )

# Configuration/Dropdown endpoints
@router.get("/config/items")
async def get_items_dropdown(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get items for dropdown in packing list creation/editing.
    Returns consumables from masters_consumables table.
    """
    try:
        # Try to get from masters_consumables first
        try:
            consumables = db.query(MastersConsumable).filter(
                MastersConsumable.status == True
            ).all()
            
            items = []
            for consumable in consumables:
                items.append({
                    "id": str(consumable.id),
                    "name": consumable.name,
                    "value": consumable.name,
                    "label": consumable.name,
                    "itemNo": consumable.item_no,
                    "unit": consumable.unit,
                    "category": consumable.category.name if consumable.category else "Unknown"
                })
            
            if items:
                return {
                    "success": True,
                    "data": {"items": items}
                }
        except Exception as e:
            logger.warning(f"Could not fetch from masters_consumables: {e}")
        
        # Fallback to default items matching the screenshots
        default_items = [
            {"id": "1", "name": "AV Cables", "value": "AV Cables", "label": "AV Cables", "itemNo": "AVC001", "unit": "pcs", "category": "Electronics"},
            {"id": "2", "name": "ADP Supply Kit", "value": "ADP Supply Kit", "label": "ADP Supply Kit", "itemNo": "ASK001", "unit": "kit", "category": "Supply"},
            {"id": "3", "name": "Envelopes", "value": "Envelopes", "label": "Envelopes", "itemNo": "ENV001", "unit": "pcs", "category": "Stationery"},
            {"id": "4", "name": "Printer Paper", "value": "Printer Paper", "label": "Printer Paper", "itemNo": "PP001", "unit": "ream", "category": "Stationery"},
            {"id": "5", "name": "Ballot Box", "value": "Ballot Box", "label": "Ballot Box", "itemNo": "BB001", "unit": "pcs", "category": "Equipment"},
            {"id": "6", "name": "Voting Booth", "value": "Voting Booth", "label": "Voting Booth", "itemNo": "VB001", "unit": "pcs", "category": "Equipment"},
            {"id": "7", "name": "Backup Power Supply", "value": "Backup Power Supply", "label": "Backup Power Supply", "itemNo": "PS001", "unit": "pcs", "category": "Electronics"},
            {"id": "8", "name": "Emergency Phone", "value": "Emergency Phone", "label": "Emergency Phone", "itemNo": "EP001", "unit": "pcs", "category": "Electronics"}
        ]
        
        return {
            "success": True,
            "data": {"items": default_items}
        }
        
    except Exception as e:
        logger.error(f"Get items dropdown error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching items"
        )

@router.get("/config/locations")
async def get_locations_dropdown(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get locations for dropdown in packing list creation/editing.
    Returns locations matching the screenshots.
    """
    try:
        # Default locations exactly as shown in screenshots
        default_locations = [
            {"id": "1", "name": "Compartment 1", "value": "Compartment 1", "label": "Compartment 1", "type": "Storage", "source": "default"},
            {"id": "2", "name": "Compartment 2", "value": "Compartment 2", "label": "Compartment 2", "type": "Storage", "source": "default"},
            {"id": "3", "name": "Compartment 3", "value": "Compartment 3", "label": "Compartment 3", "type": "Storage", "source": "default"},
            {"id": "4", "name": "Main Storage", "value": "Main Storage", "label": "Main Storage", "type": "Storage", "source": "default"},
            {"id": "5", "name": "Warehouse A", "value": "Warehouse A", "label": "Warehouse A", "type": "Warehouse", "source": "default"},
            {"id": "6", "name": "Warehouse B", "value": "Warehouse B", "label": "Warehouse B", "type": "Warehouse", "source": "default"},
            {"id": "7", "name": "Mobile Unit 1", "value": "Mobile Unit 1", "label": "Mobile Unit 1", "type": "Mobile", "source": "default"},
            {"id": "8", "name": "Mobile Unit 2", "value": "Mobile Unit 2", "label": "Mobile Unit 2", "type": "Mobile", "source": "default"}
        ]
        
        return {
            "success": True,
            "data": {"locations": default_locations}
        }
    except Exception as e:
        logger.error(f"Get locations dropdown error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error fetching locations"
        )

# Generate next Ref No endpoint
@router.get("/config/next-ref-no")
async def get_next_ref_no(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get the next available ref number for auto-filling the form.
    """
    try:
        next_ref_no = MasterPackingList.generate_next_ref_no(db)
        
        return {
            "success": True,
            "data": {"refNo": next_ref_no}
        }
    except Exception as e:
        logger.error(f"Get next ref no error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error generating next reference number"
        ) 