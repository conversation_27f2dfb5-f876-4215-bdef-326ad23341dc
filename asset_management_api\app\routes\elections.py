# app/routes/elections.py
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc
from app.config.database import get_db
from app.models.elections import Election, ElectionFile, ElectionNote
from app.models.election_type import ElectionType
from app.models.user import User
from app.schemas.elections import (
    ElectionCreate,
    ElectionUpdate,
    ElectionResponse,
    ElectionListResponse,
    ElectionDetailResponse,
    ElectionFileCreate,
    ElectionFileResponse,
    ElectionNoteCreate,
    ElectionNoteResponse
)
from app.middleware.auth import get_current_user
from uuid import uuid4
from datetime import datetime
import logging
import os

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/", response_model=ElectionListResponse)
@router.get("", response_model=ElectionListResponse)
async def get_elections(
    page: int = 1,
    limit: int = 10,
    search: str = "",
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Get all elections with pagination and search.
    """
    try:
        logger.info(f"🗳️ GET /elections called with page={page}, limit={limit}, search='{search}'")
        
        # Calculate offset
        offset = (page - 1) * limit
        
        # Base query with joins
        query = db.query(Election).options(
            joinedload(Election.election_type)
        )
        
        # Apply search filter if provided
        if search:
            search_filter = or_(
                Election.name.ilike(f"%{search}%"),
                ElectionType.name.ilike(f"%{search}%")
            )
            query = query.join(ElectionType).filter(search_filter)
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination and ordering
        elections = query.order_by(desc(Election.created_at)).offset(offset).limit(limit).all()
        
        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit
        
        logger.info(f"✅ Found {len(elections)} elections (total: {total_count})")
        
        return {
            "success": True,
            "data": {
                "elections": [election.to_dict() for election in elections],
                "pagination": {
                    "current_page": page,
                    "total_pages": total_pages,
                    "total_items": total_count,
                    "items_per_page": limit,
                    "has_next": page < total_pages,
                    "has_prev": page > 1
                }
            }
        }
    except Exception as e:
        logger.error(f"❌ Get elections error: {e}")
        logger.error(f"❌ Error type: {type(e)}")
        import traceback
        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/{election_id}", response_model=ElectionDetailResponse)
async def get_election(
    election_id: str,
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Get election by ID with all related data.
    """
    try:
        election = db.query(Election).options(
            joinedload(Election.election_type),
            joinedload(Election.election_files).joinedload(ElectionFile.user),
            joinedload(Election.election_notes).joinedload(ElectionNote.user)
        ).filter(Election.id == election_id).first()
        
        if not election:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Election not found"
            )
        
        return {
            "success": True,
            "data": election.to_dict()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get election error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.post("/", response_model=ElectionDetailResponse)
@router.post("", response_model=ElectionDetailResponse)
async def create_election(
    election_data: ElectionCreate,
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Create a new election.
    """
    try:
        logger.info(f"🗳️ POST /elections called with data: {election_data}")
        
        # Verify election type exists
        election_type = db.query(ElectionType).filter(
            ElectionType.id == election_data.election_type_id
        ).first()
        
        if not election_type:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid election type"
            )
        
        # Check if election name already exists
        existing_election = db.query(Election).filter(
            Election.name == election_data.name
        ).first()
        
        if existing_election:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Election with this name already exists"
            )
        
        # Create new election
        new_election = Election(
            id=str(uuid4()),
            name=election_data.name,
            election_type_id=election_data.election_type_id,
            date=election_data.election_date,  # Map election_date to date column
            status='Active' if election_data.status else 'Inactive',  # Convert boolean to string status
            description=election_data.description
        )
        
        db.add(new_election)
        db.commit()
        db.refresh(new_election)
        
        # Load the election with relationships
        election = db.query(Election).options(
            joinedload(Election.election_type)
        ).filter(Election.id == new_election.id).first()
        
        return {
            "success": True,
            "data": election.to_dict(),
            "message": "Election created successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Create election error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.put("/{election_id}", response_model=ElectionDetailResponse)
async def update_election(
    election_id: str,
    election_data: ElectionUpdate,
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Update an existing election.
    """
    try:
        # Get existing election
        election = db.query(Election).filter(Election.id == election_id).first()
        
        if not election:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Election not found"
            )
        
        # Verify election type if provided
        if election_data.election_type_id:
            election_type = db.query(ElectionType).filter(
                ElectionType.id == election_data.election_type_id
            ).first()
            
            if not election_type:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid election type"
                )
        
        # Check if new name already exists (excluding current election)
        if election_data.name and election_data.name != election.name:
            existing_election = db.query(Election).filter(
                and_(
                    Election.name == election_data.name,
                    Election.id != election_id
                )
            ).first()
            
            if existing_election:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Election with this name already exists"
                )
        
        # Update fields
        update_data = election_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            if field == 'election_date':
                # Map election_date to date column
                setattr(election, 'date', value)
            elif field == 'status':
                # Convert boolean to string status
                setattr(election, field, 'Active' if value else 'Inactive')
            else:
                setattr(election, field, value)
        
        db.commit()
        db.refresh(election)
        
        # Load the election with relationships
        election = db.query(Election).options(
            joinedload(Election.election_type)
        ).filter(Election.id == election_id).first()
        
        return {
            "success": True,
            "data": election.to_dict(),
            "message": "Election updated successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update election error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.delete("/{election_id}")
async def delete_election(
    election_id: str,
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Delete an election.
    """
    try:
        election = db.query(Election).filter(Election.id == election_id).first()
        
        if not election:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Election not found"
            )
        
        db.delete(election)
        db.commit()
        
        return {
            "success": True,
            "message": "Election deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete election error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# Election Files endpoints
@router.post("/{election_id}/files")
async def upload_election_file(
    election_id: str,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Upload a file for an election.
    """
    try:
        # Verify election exists
        election = db.query(Election).filter(Election.id == election_id).first()
        if not election:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Election not found"
            )
        
        # Create uploads directory if it doesn't exist
        upload_dir = f"uploads/elections/{election_id}"
        os.makedirs(upload_dir, exist_ok=True)
        
        # Save file
        file_path = f"{upload_dir}/{file.filename}"
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Calculate file size
        file_size = f"{len(content) / (1024 * 1024):.1f} MB"
        
        # Create file record
        election_file = ElectionFile(
            id=str(uuid4()),
            election_id=election_id,
            file_name=file.filename,
            file_path=file_path,
            file_size=file_size,
            uploaded_by=current_user.id
        )
        
        db.add(election_file)
        db.commit()
        db.refresh(election_file)
        
        return {
            "success": True,
            "data": election_file.to_dict(),
            "message": "File uploaded successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Upload election file error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/{election_id}/files")
async def get_election_files(
    election_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all files for an election.
    """
    try:
        files = db.query(ElectionFile).options(
            joinedload(ElectionFile.user)
        ).filter(ElectionFile.election_id == election_id).all()
        
        return {
            "success": True,
            "data": [file.to_dict() for file in files]
        }
    except Exception as e:
        logger.error(f"Get election files error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.delete("/{election_id}/files/{file_id}")
async def delete_election_file(
    election_id: str,
    file_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete an election file.
    """
    try:
        file = db.query(ElectionFile).filter(
            and_(
                ElectionFile.id == file_id,
                ElectionFile.election_id == election_id
            )
        ).first()
        
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )
        
        # Delete file from filesystem
        if os.path.exists(file.file_path):
            os.remove(file.file_path)
        
        db.delete(file)
        db.commit()
        
        return {
            "success": True,
            "message": "File deleted successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete election file error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

# Election Notes endpoints
@router.post("/{election_id}/notes")
async def create_election_note(
    election_id: str,
    note_data: ElectionNoteCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a note for an election.
    """
    try:
        # Verify election exists
        election = db.query(Election).filter(Election.id == election_id).first()
        if not election:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Election not found"
            )
        
        # Create note
        note = ElectionNote(
            id=str(uuid4()),
            election_id=election_id,
            note_text=note_data.note_text,
            created_by=current_user.id
        )
        
        db.add(note)
        db.commit()
        db.refresh(note)
        
        # Load note with user data
        note = db.query(ElectionNote).options(
            joinedload(ElectionNote.user)
        ).filter(ElectionNote.id == note.id).first()
        
        return {
            "success": True,
            "data": note.to_dict(),
            "message": "Note created successfully"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Create election note error: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )

@router.get("/{election_id}/notes")
async def get_election_notes(
    election_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all notes for an election.
    """
    try:
        notes = db.query(ElectionNote).options(
            joinedload(ElectionNote.user)
        ).filter(ElectionNote.election_id == election_id).order_by(
            desc(ElectionNote.created_at)
        ).all()
        
        return {
            "success": True,
            "data": [note.to_dict() for note in notes]
        }
    except Exception as e:
        logger.error(f"Get election notes error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
